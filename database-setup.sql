-- Supabase Database Setup Script for Next Generation Innovation L.L.C.
-- Run this script in your Supabase SQL Editor to create the necessary tables

-- Enable Row Level Security (RLS) policies
-- You can adjust these based on your security requirements

-- 1. Create contacts table for storing contact form submissions
CREATE TABLE IF NOT EXISTS public.contacts (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    message TEXT NOT NULL,
    company TEXT,
    subject TEXT,
    budget TEXT,
    timeline TEXT,
    services TEXT[], -- Array of services
    hear_about_us TEXT,
    source TEXT DEFAULT 'contact_form',
    page_url TEXT,
    referrer TEXT,
    utm_source TEXT,
    utm_medium TEXT,
    utm_campaign TEXT,
    user_agent TEXT,
    platform TEXT,
    language TEXT,
    screen_resolution TEXT,
    viewport TEXT,
    timezone TEXT,
    local_time TEXT,
    status TEXT DEFAULT 'new', -- new, qualified, contacted, converted, closed
    priority TEXT DEFAULT 'normal', -- low, normal, high, urgent
    lead_score INTEGER DEFAULT 0,
    form_type TEXT DEFAULT 'contact_form',
    form_version TEXT DEFAULT '1.0',
    session_id TEXT,
    browser_timezone TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create projects table for portfolio/case studies
CREATE TABLE IF NOT EXISTS public.projects (
    id BIGSERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    image_url TEXT,
    category TEXT NOT NULL,
    technologies TEXT[], -- Array of technologies
    live_url TEXT,
    github_url TEXT,
    featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create team table for team member information
CREATE TABLE IF NOT EXISTS public.team (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    bio TEXT,
    image_url TEXT,
    email TEXT,
    linkedin TEXT,
    twitter TEXT,
    github TEXT,
    website TEXT,
    featured BOOLEAN DEFAULT true,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Create blog_posts table for blog content
CREATE TABLE IF NOT EXISTS public.blog_posts (
    id BIGSERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author TEXT NOT NULL,
    image_url TEXT,
    tags TEXT[], -- Array of tags
    published BOOLEAN DEFAULT false,
    featured BOOLEAN DEFAULT false,
    slug TEXT UNIQUE,
    meta_title TEXT,
    meta_description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ
);

-- 5. Create patents table for intellectual property portfolio
CREATE TABLE IF NOT EXISTS public.patents (
    id BIGSERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    patent_number TEXT UNIQUE NOT NULL,
    year INTEGER NOT NULL,
    inventor TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    patent_url TEXT,
    status TEXT DEFAULT 'granted', -- pending, granted, expired
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Create publications table for research publications
CREATE TABLE IF NOT EXISTS public.publications (
    id BIGSERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    journal TEXT NOT NULL,
    year INTEGER NOT NULL,
    author TEXT NOT NULL,
    description TEXT NOT NULL,
    publication_url TEXT,
    doi TEXT,
    citation_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. Create homepage_content table for dynamic hero section
CREATE TABLE IF NOT EXISTS public.homepage_content (
    id BIGSERIAL PRIMARY KEY,
    section_name TEXT UNIQUE NOT NULL DEFAULT 'home_hero',
    title TEXT NOT NULL,
    subtitle TEXT NOT NULL,
    primary_button_text TEXT DEFAULT 'Explore Services',
    primary_button_url TEXT DEFAULT '/services',
    secondary_button_text TEXT DEFAULT 'View About Us',
    secondary_button_url TEXT DEFAULT '/about',
    background_image_url TEXT,
    logo_animation_enabled BOOLEAN DEFAULT true,
    scroll_indicator_enabled BOOLEAN DEFAULT true,
    scroll_indicator_text TEXT DEFAULT 'Scroll Down',
    scroll_indicator_target TEXT DEFAULT '#services',
    active BOOLEAN DEFAULT true,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contacts_created_at ON public.contacts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_contacts_status ON public.contacts(status);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON public.contacts(email);
CREATE INDEX IF NOT EXISTS idx_projects_category ON public.projects(category);
CREATE INDEX IF NOT EXISTS idx_projects_featured ON public.projects(featured);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON public.blog_posts(published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON public.blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_patents_year ON public.patents(year);
CREATE INDEX IF NOT EXISTS idx_publications_year ON public.publications(year);
CREATE INDEX IF NOT EXISTS idx_homepage_content_active ON public.homepage_content(active);
CREATE INDEX IF NOT EXISTS idx_homepage_content_section_name ON public.homepage_content(section_name);

-- Enable Row Level Security (you can customize these policies based on your needs)
ALTER TABLE public.contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.publications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.homepage_content ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (adjust as needed)
-- Contacts table - only service role can read/write (admin access)
CREATE POLICY "Enable read access for service role only" ON public.contacts
FOR SELECT USING (auth.role() = 'service_role');

CREATE POLICY "Enable insert for all users" ON public.contacts
FOR INSERT WITH CHECK (true);

-- Projects table - public read access
CREATE POLICY "Enable read access for all users" ON public.projects
FOR SELECT USING (true);

-- Team table - public read access
CREATE POLICY "Enable read access for all users" ON public.team
FOR SELECT USING (true);

-- Blog posts table - public read access for published posts
CREATE POLICY "Enable read access for published posts" ON public.blog_posts
FOR SELECT USING (published = true);

-- Patents table - public read access
CREATE POLICY "Enable read access for all users" ON public.patents
FOR SELECT USING (true);

-- Publications table - public read access
CREATE POLICY "Enable read access for all users" ON public.publications
FOR SELECT USING (true);

-- Homepage content table - public read access for active content
CREATE POLICY "Enable read access for active homepage content" ON public.homepage_content
FOR SELECT USING (active = true);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER handle_contacts_updated_at
    BEFORE UPDATE ON public.contacts
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_team_updated_at
    BEFORE UPDATE ON public.team
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_blog_posts_updated_at
    BEFORE UPDATE ON public.blog_posts
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_patents_updated_at
    BEFORE UPDATE ON public.patents
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_publications_updated_at
    BEFORE UPDATE ON public.publications
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_homepage_content_updated_at
    BEFORE UPDATE ON public.homepage_content
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample data (optional - you can remove this if you don't want sample data)

-- Sample team members
INSERT INTO public.team (name, role, bio, image_url, email, linkedin, order_index) VALUES
('Mohammed Abdun Noor', 'Managing Member', 'Mr. Noor is a dynamic leader with multi-disciplinary expertise and a proven track record across diverse industries. He has successfully managed several businesses, showcasing his strong entrepreneurial spirit and business acumen.', '/team/mohammed.jpg', '<EMAIL>', 'https://www.linkedin.com/in/mohammed-abdun-noor-1b130b37/', 1),
('Dr. Santanu Das', 'Technology and Business Advisor', 'Dr. Santanu Das is a seasoned technology executive and entrepreneur with a distinguished career spanning over four decades in the IT, telecommunications and semiconductor industries. He is the founder and former CEO of TranSwitch Corporation.', '/team/santanu.jpg', '<EMAIL>', 'https://www.linkedin.com/in/dr-santanu-das-3917201b/', 2)
ON CONFLICT DO NOTHING;

-- Sample patents
INSERT INTO public.patents (title, patent_number, year, inventor, description, category, patent_url) VALUES
('Cost optimization of wireless-enabled metering infrastructures', 'US 8619657', 2013, 'Dr. Santanu Das', 'Granted 12/31/2013. This patent focuses on optimizing costs in wireless-enabled metering infrastructures for IoT applications.', 'Internet of Things (IoT)', 'https://patents.google.com/patent/US8619657B2/en?oq=8619657+'),
('Methods systems, and devices for robustness improvement in a mobile ad hoc network using reputation-based routing', 'US 8774192', 2014, 'Dr. Santanu Das', 'Granted 7/8/2014. This patent introduces methods and systems for improving robustness in mobile ad hoc networks through reputation-based routing.', 'Networking', 'https://patents.google.com/patent/US8774192B2/en?oq=8774192+'),
('Systems, devices, and methods of managing power consumption in wireless sensor networks', 'US 8532008', 2013, 'Dr. Santanu Das', 'Granted 9/10/2013. This patent presents innovative approaches to managing power consumption in wireless sensor networks, extending operational life and efficiency.', 'Internet of Things (IoT)', 'https://patents.google.com/patent/US8532008B2/en?oq=8532008+')
ON CONFLICT DO NOTHING;

-- Sample blog posts (from your existing static data)
INSERT INTO public.blog_posts (title, content, author, image_url, tags, published, slug, created_at) VALUES
('Tech Employment In New York State and In New York City', 'Employment in the New York State tech sector totaled 321,280 jobs in 2021. New York had the third largest total after California and Texas...', 'Next Generation Innovation Marketing', 'https://images.unsplash.com/photo-*************-f38ee64cd54c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80', ARRAY['Technology', 'Employment', 'New York', 'Tech Jobs'], true, 'tech-employment-new-york', '2024-02-19T00:00:00Z'),
('What is New in Minority and Women Owned Business Enterprise Landscape in New York?', 'New York Governor Kathy Hochul signed into law a package of three bills recently that will increase oversight of the Minority-and Women-Owned Business Enterprise (MWBE) infrastructure...', 'Next Generation Innovation Marketing', 'https://images.unsplash.com/photo-1507679799987-c73779587ccf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80', ARRAY['MWBE', 'Business', 'New York', 'Policy Changes'], true, 'mwbe-landscape-new-york', '2024-05-08T00:00:00Z'),
('Innovation hotspots in New York', 'New York is a hub of innovation, and several regions within the state foster entrepreneurial growth and technological advancements...', 'Next Generation Innovation Marketing', 'https://images.unsplash.com/photo-1496568816309-51d7c20e3b21?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80', ARRAY['Innovation', 'New York', 'Technology', 'Startups'], true, 'innovation-hotspots-new-york', '2024-02-29T00:00:00Z')
ON CONFLICT DO NOTHING; 