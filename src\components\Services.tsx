import { useServices } from "@/hooks/use-firestore-data";
import { Shield, Code, GraduationCap, Heart, Database, Server, Cloud, Users, Settings, Globe, Stethoscope, Activity } from "lucide-react";

export default function Services() {
  const { data: services, loading, error } = useServices();
  
  // Function to get service icon based on category or title
  const getServiceIcon = (service: any) => {
    const category = service.category?.toLowerCase();
    const title = service.title?.toLowerCase();
    
    if (category?.includes('public_safety') || title?.includes('public safety') || title?.includes('emergency')) {
      return <Shield className="w-6 h-6 text-blue-600" />;
    }
    if (category?.includes('development') || title?.includes('application development') || title?.includes('data analytics')) {
      return <Code className="w-6 h-6 text-green-600" />;
    }
    if (category?.includes('education') || title?.includes('higher education')) {
      return <GraduationCap className="w-6 h-6 text-purple-600" />;
    }
    if (category?.includes('healthcare') || title?.includes('healthcare')) {
      return <Stethoscope className="w-6 h-6 text-red-600" />;
    }
    if (title?.includes('database') || title?.includes('data')) {
      return <Database className="w-6 h-6 text-orange-600" />;
    }
    if (title?.includes('server') || title?.includes('infrastructure')) {
      return <Server className="w-6 h-6 text-gray-600" />;
    }
    if (title?.includes('cloud') || title?.includes('platform')) {
      return <Cloud className="w-6 h-6 text-cyan-600" />;
    }
    if (title?.includes('team') || title?.includes('users')) {
      return <Users className="w-6 h-6 text-indigo-600" />;
    }
    if (title?.includes('automation') || title?.includes('system')) {
      return <Settings className="w-6 h-6 text-yellow-600" />;
    }
    // Default icon
    return <Globe className="w-6 h-6 text-gray-500" />;
  };
  


  return (
    <section id="services" className="py-20 bg-background text-foreground">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our Services
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We offer comprehensive IT solutions tailored to your needs
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {loading && <div className="col-span-full text-center">Loading services...</div>}
          {error && <div className="col-span-full text-center text-red-500">{error}</div>}
          {!loading && !error && services.length === 0 && (
            <div className="col-span-full text-center">No services found.</div>
          )}
          {services.map(service => (
            <div key={service.id} className="bg-card text-card-foreground p-6 rounded-lg border border-border shadow-sm flex flex-col items-center">
              <div className="mb-6 flex items-center justify-center">
                {service.icon ? (
                  <div className="text-5xl">{service.icon}</div>
                ) : service.image_url ? (
                  <img src={service.image_url} alt={service.title} className="w-20 h-20 object-contain" />
                ) : (
                  <div className="w-16 h-16 flex items-center justify-center bg-primary/10 rounded-full">
                    {getServiceIcon(service)}
                  </div>
                )}
              </div>
              <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
              
              {/* Display main description as regular paragraph */}
              {(service.description || service.Description) && (
                <p className="text-muted-foreground mb-4 text-center text-sm leading-relaxed">
                  {service.description || service.Description}
                </p>
              )}
              
              {/* Display points if they exist */}
              {service.points && service.points.length > 0 && (
                <div className="w-full space-y-3 mt-4">
                  {service.points.map((point: any, idx: number) => (
                    <div key={idx} className="text-sm p-3 bg-muted/50 rounded-lg border-l-4 border-primary/20">
                      
                      <span className="text-foreground leading-relaxed">
                        <span className="font-semibold">{point.title}:</span>{' '}
                        <span className="text-muted-foreground">
                          {point.description || point.Description || 'No description available'}
                        </span>
                      </span>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Fallback for old features structure */}
              {(!service.points || service.points.length === 0) && service.features && service.features.length > 0 && (
                <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                  {service.features.map((feature: string, idx: number) => (
                    <li key={idx}>{feature}</li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
