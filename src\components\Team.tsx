import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import TeamCard from "./TeamCard";
import { useQuery } from "@tanstack/react-query";
import { getTeamMembers } from "@/lib/firebase";

// Fallback team data in case of error
const fallbackTeam: Array<{
  id: number;
  name: string;
  role: string;
  bio: string;
  imageUrl: string;
  email: string;
  social?: {
    linkedin?: string;
    twitter?: string;
  };
}> = [];

export default function Team() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.2 });
  
  // Fetch team data from Firebase
  const { data: team = fallbackTeam, isLoading } = useQuery({
    queryKey: ['team'],
    queryFn: getTeamMembers
  });
  
  // Map Firestore data to match TeamCard interface
  const mappedTeam = team.map((member: any) => ({
    id: member.id || member.name,
    name: member.name || 'Unknown',
    role: member.title || member.role || 'Team Member',
    bio: member.bio || 'No bio available',
    imageUrl: member.imageUrl || member.image_url || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    email: member.email || '',
    social: {
      linkedin: member.linkedinUrl || member.linkedin || member.social?.linkedin,
      twitter: member.twitter || member.social?.twitter
    }
  }));

  return (
    <section id="team" ref={ref} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Team</h2>
            <div className="w-20 h-1 bg-indigo-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Meet our talented team of professionals dedicated to delivering exceptional IT solutions.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {mappedTeam.map((member: any, index: number) => (
            <TeamCard
              key={member.id}
              name={member.name}
              role={member.role}
              bio={member.bio}
              imageUrl={member.imageUrl}
              email={member.email}
              social={member.social}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
