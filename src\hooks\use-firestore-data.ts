import { useState, useEffect } from 'react';
import { getHomeContent, getAboutContent, getBlogContent, getServices } from '@/lib/firebase';

// Type definitions
interface HomeContent {
  title?: string;
  subtitle?: string;
  sectionHeading?: string;
  sectionSubheading?: string;
  missionHeading?: string;
  missionText1?: string;
  missionText2?: string;
  missionBadge?: string;
  primary_button_text?: string;
  primary_button_url?: string;
  secondary_button_text?: string;
  secondary_button_url?: string;
  scroll_indicator_text?: string;
  scroll_indicator_target?: string;
  [key: string]: any;
}

export function useHomeContent() {
  const [data, setData] = useState<HomeContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("useHomeContent hook - fetching data...");
        const content = await getHomeContent();
        console.log("useHomeContent hook - received content:", content);
        setData(content);
      } catch (err) {
        console.error("useHomeContent hook - error:", err);
        setError(err instanceof Error ? err.message : 'Failed to fetch home content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

// Type definitions for Blog page
interface BlogContent {
  heroTitle?: string;
  heroSubtitle?: string;
  [key: string]: any;
}

export function useBlogContent() {
  const [data, setData] = useState<BlogContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("useBlogContent hook - fetching data...");
        const content = await getBlogContent();
        console.log("useBlogContent hook - received content:", content);
        setData(content);
      } catch (err) {
        console.error("useBlogContent hook - error:", err);
        setError(err instanceof Error ? err.message : 'Failed to fetch blog content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

// Type definitions for About page
interface AboutContent {
  heroTitle?: string;
  heroSubtitle?: string;
  missionTitle?: string;
  missionSubtitle?: string;
  missionDescription?: string;
  targetCustomersTitle?: string;
  targetCustomersSubtitle?: string;
  targetCustomersPoint1?: string;
  targetCustomersPoint2?: string;
  targetCustomersPoint3?: string;
  valuePropositionTitle?: string;
  valuePropositionCard1Heading?: string;
  valuePropositionCard1Subheading?: string;
  valuePropositionCard2Heading?: string;
  valuePropositionCard2Subheading?: string;
  valuePropositionCard3Heading?: string;
  valuePropositionCard3Subheading?: string;
  valuePropositionCard4Heading?: string;
  valuePropositionCard4Subheading?: string;
  valuePropositionCard5Heading?: string;
  valuePropositionCard5Subheading?: string;
  valuePropositionCard6Heading?: string;
  valuePropositionCard6Subheading?: string;
  [key: string]: any;
}

export function useAboutContent() {
  const [data, setData] = useState<AboutContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("useAboutContent hook - fetching data...");
        const content = await getAboutContent();
        console.log("useAboutContent hook - received content:", content);
        setData(content);
      } catch (err) {
        console.error("useAboutContent hook - error:", err);
        setError(err instanceof Error ? err.message : 'Failed to fetch about content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

// Type definitions for Services
interface Service {
  id: string;
  title: string;
  description: string;
  icon?: string;
  image_url?: string;
  features?: string[];
  points?: Array<{
    title: string;
    description: string;
    order: number;
  }>;
  category: string;
  order?: number;
  createdAt?: any;
  updatedAt?: any;
  [key: string]: any;
}

export function useServices() {
  const [data, setData] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const services = await getServices();
        console.log('Services fetched from Firestore:', services);
        setData(services);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch services');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
} 