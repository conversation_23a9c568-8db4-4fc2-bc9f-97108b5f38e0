import { useState, useEffect } from 'react';
import { 
  getHomepageContent, 
  getProjects, 
  getTeamMembers, 
  getBlogPosts,
  getBlogPageContent,
  getServices
} from '@/lib/supabase';

// Type definitions
interface HomepageContent {
  id?: number;
  title?: string;
  subtitle?: string;
  primary_button_text?: string;
  primary_button_url?: string;
  secondary_button_text?: string;
  secondary_button_url?: string;
  scroll_indicator_text?: string;
  scroll_indicator_target?: string;
  [key: string]: any;
}

interface Project {
  id: number;
  title: string;
  description: string;
  image_url: string;
  category: string;
  technologies: string[];
  live_url?: string;
  github_url?: string;
  created_at: string;
  [key: string]: any;
}

interface TeamMember {
  id: number;
  name: string;
  role: string;
  bio: string;
  image_url: string;
  email?: string;
  linkedin?: string;
  created_at: string;
  [key: string]: any;
}

interface BlogPost {
  id: number;
  title: string;
  content: string;
  excerpt: string;
  author: string;
  image_url: string;
  tags: string[];
  published: boolean;
  featured: boolean;
  slug: string;
  meta_title: string;
  meta_description: string;
  created_at: string;
  updated_at: string;
  published_at: string;
  [key: string]: any;
}

interface Service {
  id: number;
  title: string;
  description: string;
  icon?: string;
  image_url?: string;
  features?: string[];
  points?: Array<{
    title: string;
    description: string;
    order: number;
  }>;
  category: string;
  created_at: string;
  [key: string]: any;
}

export function useHomepageContent() {
  const [data, setData] = useState<HomepageContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const content = await getHomepageContent();
        setData(content);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch homepage content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

export function useProjects() {
  const [data, setData] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const projects = await getProjects();
        setData(projects);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch projects');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

export function useTeamMembers() {
  const [data, setData] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const team = await getTeamMembers();
        setData(team);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch team members');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

export function useBlogPosts() {
  const [data, setData] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const posts = await getBlogPosts();
        setData(posts);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

export function useBlogPageContent() {
  const [data, setData] = useState<HomepageContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const content = await getBlogPageContent();
        setData(content);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch blog page content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

export function useServices() {
  const [data, setData] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const services = await getServices();
        setData(services);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch services');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
} 