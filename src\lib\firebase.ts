import { initializeApp } from "firebase/app";
import { getFirestore, collection, addDoc, getDocs, doc, setDoc } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyCO7tVx674aYAOZ8esqDuUiANZvvr1ZUAY",
  authDomain: "next-generation-4c0ea.firebaseapp.com",
  databaseURL: "https://next-generation-4c0ea-default-rtdb.firebaseio.com",
  projectId: "next-generation-4c0ea",
  storageBucket: "next-generation-4c0ea.firebasestorage.app",
  messagingSenderId: "487073716513",
  appId: "1:487073716513:web:83ef258953a8e7b07f8660"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

console.log("Firebase initialized with project ID:", firebaseConfig.projectId);
console.log("Firestore database instance created");

// Firestore database helper functions
export const saveContact = async (contactData: any) => {
  try {
    console.log("Attempting to save contact to Firestore:", contactData);
    console.log("Firestore database instance:", db);
    
    const docRef = await addDoc(collection(db, 'contacts'), {
      ...contactData,
      createdAt: new Date().toISOString(),
      timestamp: new Date()
    });
    
    console.log("Contact saved successfully with ID: ", docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error("Error saving contact to Firestore:", error);
    console.error("Error details:", {
      code: (error as any).code,
      message: (error as any).message,
      stack: (error as any).stack
    });
    return { success: false, error };
  }
};

export const getProjects = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'projects'));
    const projects: any[] = [];
    querySnapshot.forEach((doc) => {
      projects.push({ id: doc.id, ...doc.data() });
    });
    return projects;
  } catch (error) {
    console.error("Error fetching projects:", error);
    return [];
  }
};

export const getTeamMembers = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'team_members'));
    const teamMembers: any[] = [];
    querySnapshot.forEach((doc) => {
      teamMembers.push({ id: doc.id, ...doc.data() });
    });
    // Sort team members by order field (smaller numbers on top)
    teamMembers.sort((a, b) => (a.order || 0) - (b.order || 0));
    console.log("Team members fetched from Firestore:", teamMembers);
    return teamMembers;
  } catch (error) {
    console.error("Error fetching team members:", error);
    return [];
  }
};

export const getTeamContent = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'team_content'));
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("Team content fetched from Firestore:", data);
      return data;
    }
    return null;
  } catch (error) {
    console.error("Error fetching team content:", error);
    return null;
  }
};

export const getBlogPosts = async () => {
  try {
    console.log("Starting to fetch blog posts from Firestore...");
    const querySnapshot = await getDocs(collection(db, 'blogs'));
    console.log("Query snapshot size:", querySnapshot.size);
    console.log("Query snapshot empty:", querySnapshot.empty);
    
    const blogPosts: any[] = [];
    querySnapshot.forEach((doc) => {
      console.log("Processing document ID:", doc.id);
      const data = doc.data();
      console.log("Document data:", data);
      
      const blogPost = {
        id: doc.id,
        title: data.heading || data.title || "Untitled",
        content: data.description || data.content || "",
        excerpt: data.excerpt || data.description?.substring(0, 150) + "..." || "",
        author: data.author || "Unknown Author",
        date: data.date || data.createdAt?.toDate?.()?.toISOString()?.split('T')[0] || new Date().toISOString().split('T')[0],
        imageUrl: data.imageUrl || data.image_url || undefined,
        tags: Array.isArray(data.tags) ? data.tags : [],
        published: data.published !== false, // Default to true if not specified
        featured: data.featured || false,
        slug: data.slug || doc.id,
        meta_title: data.meta_title || data.heading || data.title,
        meta_description: data.meta_description || data.description?.substring(0, 160) || "",
        created_at: data.createdAt?.toDate?.()?.toISOString() || data.created_at || new Date().toISOString(),
        updated_at: data.updatedAt?.toDate?.()?.toISOString() || data.updated_at || new Date().toISOString(),
        published_at: data.publishedAt?.toDate?.()?.toISOString() || data.published_at || data.createdAt?.toDate?.()?.toISOString()
      };
      
      console.log("Processed blog post:", blogPost);
      blogPosts.push(blogPost);
    });
    
    // Sort by creation date (newest first)
    blogPosts.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    
    console.log("Total blog posts fetched from Firestore:", blogPosts.length);
    console.log("All blog posts:", blogPosts);
    return blogPosts;
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    return [];
  }
};

export const getBlogPageContent = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'blog_page_content'));
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("Blog page content fetched from Firestore:", data);
      return data;
    }
    return null;
  } catch (error) {
    console.error("Error fetching blog page content:", error);
    return null;
  }
};

export const getBlogContent = async () => {
  try {
    console.log("getBlogContent - starting to fetch from blogContent collection");
    const querySnapshot = await getDocs(collection(db, 'blogContent'));
    console.log("getBlogContent - querySnapshot size:", querySnapshot.size);
    console.log("getBlogContent - querySnapshot empty:", querySnapshot.empty);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("getBlogContent - raw data from Firestore:", data);
      
      const result = {
        heroTitle: data.heroTitle || "Our Blog",
        heroSubtitle: data.heroSubtitle || "Insights, news, and updates from our team"
      };
      
      console.log("getBlogContent - returning mapped result:", result);
      return result;
    }
    console.log("getBlogContent - no documents found, returning null");
    return null;
  } catch (error) {
    console.error("getBlogContent - error:", error);
    return null;
  }
};

export const getIPPortfolioContent = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'ip_portfolio_content'));
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("IP Portfolio content fetched from Firestore:", data);
      return data;
    }
    return null;
  } catch (error) {
    console.error("Error fetching IP Portfolio content:", error);
    return null;
  }
};

export const getPatents = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'patents'));
    const patents: any[] = [];
    querySnapshot.forEach((doc) => {
      patents.push({ id: doc.id, ...doc.data() });
    });
    console.log("Patents fetched from Firestore:", patents);
    return patents;
  } catch (error) {
    console.error("Error fetching patents:", error);
    return [];
  }
};

export const getPapers = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'papers'));
    const papers: any[] = [];
    querySnapshot.forEach((doc) => {
      papers.push({ id: doc.id, ...doc.data() });
    });
    console.log("Papers fetched from Firestore:", papers);
    return papers;
  } catch (error) {
    console.error("Error fetching papers:", error);
    return [];
  }
};

export const getServices = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'services'));
    const services: any[] = [];
    querySnapshot.forEach((doc) => {
      services.push({ id: doc.id, ...doc.data() });
    });
    // Sort services by order field
    services.sort((a, b) => (a.order || 0) - (b.order || 0));
    console.log("Services fetched from Firestore:", services);
    return services;
  } catch (error) {
    console.error("Error fetching services:", error);
    return [];
  }
};

export const getServicesContent = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'services_content'));
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("Services content fetched from Firestore:", data);
      return data;
    }
    return null;
  } catch (error) {
    console.error("Error fetching services content:", error);
    return null;
  }
};

export const getContactPageContent = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'contactPageContent'));
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("Contact page content fetched from Firestore:", data);
      return data;
    }
    return null;
  } catch (error) {
    console.error("Error fetching contact page content:", error);
    return null;
  }
};

export const getHomeContent = async () => {
  try {
    console.log("getHomeContent - starting to fetch from homeContent collection");
    const querySnapshot = await getDocs(collection(db, 'homeContent'));
    console.log("getHomeContent - querySnapshot size:", querySnapshot.size);
    console.log("getHomeContent - querySnapshot empty:", querySnapshot.empty);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("getHomeContent - raw data from Firestore:", data);
      console.log("getHomeContent - heroHeading:", data.heroHeading);
      console.log("getHomeContent - heroSubheading:", data.heroSubheading);
      console.log("getHomeContent - sectionHeading:", data.sectionHeading);
      console.log("getHomeContent - sectionSubheading:", data.sectionSubheading);
      console.log("getHomeContent - secondSectionText1:", data.secondSectionText1);
      console.log("getHomeContent - secondSectionText2:", data.secondSectionText2);
      console.log("getHomeContent - secondSectionText3:", data.secondSectionText3);
      console.log("getHomeContent - secondSectionText4:", data.secondSectionText4);
      
      const result = {
        title: data.heroHeading || "Next Generation Innovation L.L.C.",
        subtitle: data.heroSubheading || "Empowering Government Organizations through Innovative IT Solutions",
        sectionHeading: data.sectionHeading || "We Make Things Happen",
        sectionSubheading: data.sectionSubheading || "Making IT Solutions Easy",
        missionHeading: data.secondSectionText1 || "Innovation for State and City Government",
        missionText1: data.secondSectionText2 || "At Next Generation Innovation L.L.C, we specialize in delivering cutting-edge IT solutions tailored for state and local government organizations across the Tri-State area. From public safety and emergency management to data analytics and transportation systems, we provide both OEM and customized solutions designed to meet specific needs.",
        missionText2: data.secondSectionText3 || "We are minority owned, based in New York State, and we provide support locally.",
        missionBadge: data.secondSectionText4 || "Certified Minority-Owned Business",
        primary_button_text: "Explore Services",
        primary_button_url: "/services",
        secondary_button_text: "View About Us", 
        secondary_button_url: "/about",
        scroll_indicator_text: "Scroll Down",
        scroll_indicator_target: "#services"
      };
      
      console.log("getHomeContent - returning mapped result:", result);
      return result;
    }
    console.log("getHomeContent - no documents found, returning null");
    return null;
  } catch (error) {
    console.error("getHomeContent - error:", error);
    return null;
  }
};

export const getAboutContent = async () => {
  try {
    console.log("getAboutContent - starting to fetch from about collection");
    const querySnapshot = await getDocs(collection(db, 'about'));
    console.log("getAboutContent - querySnapshot size:", querySnapshot.size);
    console.log("getAboutContent - querySnapshot empty:", querySnapshot.empty);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      console.log("getAboutContent - raw data from Firestore:", data);
      
      const result = {
        heroTitle: data.heroTitle || "About Us",
        heroSubtitle: data.heroSubtitle || "Empowering state and local governments with next-generation technology solutions",
        missionTitle: data.missionTitle || "Our Mission",
        missionSubtitle: data.missionSubtitle || "To deliver innovative IT solutions tailored to our client's needs.",
        missionDescription: data.missionDescription || "We combine cutting-edge technology with deep industry expertise to create solutions that transform how government organizations operate and serve their communities.",
        targetCustomersTitle: data.targetCustomersTitle || "Target Customers",
        targetCustomersSubtitle: data.targetCustomersSubtitle || "State and local government organizations in the Tri-State area.",
        targetCustomersPoint1: data.targetCustomersPoint1 || "Target customers are the state and local government organizations in the Tri-State area.",
        targetCustomersPoint2: data.targetCustomersPoint2 || "We focus on providing IT solutions for public safety, emergency management, traffic management, transportation systems, and higher education.",
        targetCustomersPoint3: data.targetCustomersPoint3 || "We provide OEM solutions as well as custom IT products and services.",
        valuePropositionTitle: data.valuePropositionTitle || "Our Value Proposition",
        valuePropositionCard1Heading: data.valuePropositionCard1Heading || "Innovation",
        valuePropositionCard1Subheading: data.valuePropositionCard1Subheading || "Track record of fostering creativity, generating novel ideas, and transforming them into valuable products and services.",
        valuePropositionCard2Heading: data.valuePropositionCard2Heading || "Compliance and Security",
        valuePropositionCard2Subheading: data.valuePropositionCard2Subheading || "Compliance with industry standards, particularly in areas such as data security and privacy.",
        valuePropositionCard3Heading: data.valuePropositionCard3Heading || "Tailored Solutions",
        valuePropositionCard3Subheading: data.valuePropositionCard3Subheading || "Customized IT solutions tailored to the specific needs of our customers.",
        valuePropositionCard4Heading: data.valuePropositionCard4Heading || "Reliability and Continuity",
        valuePropositionCard4Subheading: data.valuePropositionCard4Subheading || "Focus on delivering reliable IT services that ensure continuity of operations for our customers.",
        valuePropositionCard5Heading: data.valuePropositionCard5Heading || "Digital Transformation",
        valuePropositionCard5Subheading: data.valuePropositionCard5Subheading || "Experience in driving digital transformation initiatives that can modernize processes, enhance stakeholder services, and improve overall efficiency and effectiveness.",
        valuePropositionCard6Heading: data.valuePropositionCard6Heading || "Local Expertise and Support",
        valuePropositionCard6Subheading: data.valuePropositionCard6Subheading || "Minority Owned, based in New York State, and provides support locally."
      };
      
      console.log("getAboutContent - returning mapped result:", result);
      return result;
    }
    console.log("getAboutContent - no documents found, returning null");
    return null;
  } catch (error) {
    console.error("getAboutContent - error:", error);
    return null;
  }
};

// Initialize Firestore with sample data if not exists
export const initializeDatabaseIfEmpty = async () => {
  try {
    // Check if projects exist
    const projectsSnapshot = await getDocs(collection(db, 'projects'));
    if (projectsSnapshot.empty) {
      // Sample projects data
      const projectsData = [
        {
          id: 1,
          title: "E-Commerce Platform",
          description: "A full-featured e-commerce platform with payment integration and inventory management.",
          imageUrl: "https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "web",
          technologies: ["React", "Node.js", "MongoDB", "Stripe"],
          liveUrl: "https://example.com/ecommerce",
          githubUrl: "https://github.com/example/ecommerce"
        },
        {
          id: 2,
          title: "Financial Dashboard",
          description: "Interactive financial dashboard with real-time data visualization and analytics.",
          imageUrl: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "web",
          technologies: ["Vue.js", "D3.js", "Firebase", "TailwindCSS"],
          liveUrl: "https://example.com/dashboard",
          githubUrl: "https://github.com/example/dashboard"
        },
        {
          id: 3,
          title: "Healthcare Mobile App",
          description: "Patient management mobile application for healthcare providers with telehealth features.",
          imageUrl: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "mobile",
          technologies: ["React Native", "Firebase", "Twilio"],
          liveUrl: "https://example.com/healthcare-app",
          githubUrl: "https://github.com/example/healthcare-app"
        },
        {
          id: 4,
          title: "Cloud-Based CRM",
          description: "Enterprise customer relationship management system with advanced analytics.",
          imageUrl: "https://images.unsplash.com/photo-**********-d307ca884978?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "web",
          technologies: ["Angular", "Express", "PostgreSQL", "AWS"],
          liveUrl: "https://example.com/crm",
          githubUrl: "https://github.com/example/crm"
        },
        {
          id: 5,
          title: "Inventory Management System",
          description: "Real-time inventory tracking system with barcode scanning and forecasting.",
          imageUrl: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "software",
          technologies: ["Python", "Django", "React", "PostgreSQL"],
          liveUrl: "https://example.com/inventory",
          githubUrl: "https://github.com/example/inventory"
        },
        {
          id: 6,
          title: "Fitness Tracking App",
          description: "Mobile application for tracking workouts, nutrition, and health metrics.",
          imageUrl: "https://images.unsplash.com/photo-**********-09e3ac987658?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "mobile",
          technologies: ["Flutter", "Firebase", "TensorFlow"],
          liveUrl: "https://example.com/fitness",
          githubUrl: "https://github.com/example/fitness"
        }
      ];
      
      // Add each project to Firestore
      for (let i = 0; i < projectsData.length; i++) {
        await setDoc(doc(db, 'projects', `project-${i + 1}`), projectsData[i]);
      }
    }
    
    // Check if team members exist
    const teamSnapshot = await getDocs(collection(db, 'team'));
    if (teamSnapshot.empty) {
      // Sample team data
      const teamData = [
        {
          id: 1,
          name: "Sarah Johnson",
          role: "CEO & Founder",
          bio: "With over 15 years of experience in IT and software development, Sarah leads our company's vision and strategy.",
          imageUrl: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          email: "<EMAIL>",
          social: {
            linkedin: "https://linkedin.com",
            twitter: "https://twitter.com"
          }
        },
        {
          id: 2,
          name: "David Chen",
          role: "CTO",
          bio: "David oversees all technical aspects of the company, ensuring we deliver cutting-edge solutions to our clients.",
          imageUrl: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          email: "<EMAIL>",
          social: {
            linkedin: "https://linkedin.com",
            twitter: "https://twitter.com"
          }
        },
        {
          id: 3,
          name: "Emily Rodriguez",
          role: "Lead Designer",
          bio: "Emily brings creativity and user-centered design principles to all our projects, creating beautiful and functional interfaces.",
          imageUrl: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          email: "<EMAIL>",
          social: {
            linkedin: "https://linkedin.com"
          }
        },
        {
          id: 4,
          name: "Michael Patel",
          role: "Senior Developer",
          bio: "Michael specializes in complex backend systems and cloud architecture, solving the most challenging technical problems.",
          imageUrl: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          email: "<EMAIL>",
          social: {
            linkedin: "https://linkedin.com",
            twitter: "https://twitter.com"
          }
        },
        {
          id: 5,
          name: "Alexandra Kim",
          role: "Project Manager",
          bio: "Alex ensures that projects are delivered on time and within scope, maintaining clear communication with clients.",
          imageUrl: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          email: "<EMAIL>",
          social: {
            linkedin: "https://linkedin.com"
          }
        }
      ];
      
      // Add each team member to Firestore
      for (let i = 0; i < teamData.length; i++) {
        await setDoc(doc(db, 'team', `member-${i + 1}`), teamData[i]);
      }
    }
    
    // Check if services exist
    const servicesSnapshot = await getDocs(collection(db, 'services'));
    if (servicesSnapshot.empty) {
      // Sample services data
      const servicesData = [
        {
          id: 1,
          title: "Public Safety & Emergency Services",
          description: "Advanced IT solutions to enhance public safety operations and emergency response systems.",
          icon: "🛡️",
          category: "public_safety",
          order: 1,
          points: [
            {
              title: "Emergency Response Systems",
              description: "Real-time emergency response coordination and dispatch systems",
              order: 1
            },
            {
              title: "Public Safety Communications",
              description: "Secure communication networks for law enforcement and emergency services",
              order: 2
            },
            {
              title: "Incident Management",
              description: "Comprehensive incident tracking and management platforms",
              order: 3
            },
            {
              title: "Disaster Recovery Planning",
              description: "Robust disaster recovery and business continuity solutions",
              order: 4
            },
            {
              title: "Crisis Coordination Platforms",
              description: "Multi-agency coordination systems for crisis management",
              order: 5
            }
          ]
        },
        {
          id: 2,
          title: "Application Development & Data Analytics",
          description: "Custom applications and advanced analytics solutions to transform data into actionable insights.",
          icon: "💻",
          category: "development",
          order: 2,
          points: [
            {
              title: "Custom Application Development",
              description: "Tailored software solutions designed for your specific business needs",
              order: 1
            },
            {
              title: "Legacy System Modernization",
              description: "Upgrade and modernize existing systems for improved efficiency",
              order: 2
            },
            {
              title: "Predictive Analytics",
              description: "Advanced analytics to forecast trends and make data-driven decisions",
              order: 3
            },
            {
              title: "Data Visualization",
              description: "Interactive dashboards and visualizations for better data understanding",
              order: 4
            },
            {
              title: "Machine Learning Solutions",
              description: "AI-powered solutions to automate processes and gain insights",
              order: 5
            }
          ]
        },
        {
          id: 3,
          title: "Higher Education",
          description: "A comprehensive suite of IT solutions designed specifically for universities and educational institutions.",
          icon: "🎓",
          category: "education",
          order: 3,
          points: [
            {
              title: "Student Information Systems (SIS)",
              description: "Streamlined enrollment, records, and academic tracking for better student lifecycle management",
              order: 1
            },
            {
              title: "Digital Learning Platforms",
              description: "Customized e-learning solutions that support hybrid and remote education models",
              order: 2
            },
            {
              title: "Administrative Automation",
              description: "Tools to modernize admissions, scheduling, HR, and finance operations",
              order: 3
            },
            {
              title: "Data Analytics & Reporting",
              description: "Actionable insights to support strategic planning and institutional improvement",
              order: 4
            },
            {
              title: "IT Infrastructure & Support",
              description: "Scalable networks and reliable support services to keep educational environments connected and productive",
              order: 5
            }
          ]
        },
        {
          id: 4,
          title: "Healthcare",
          description: "We deliver specialized IT solutions to healthcare institutions to improve patient care, enhance medical education, and streamline operations.",
          icon: "🏥",
          category: "healthcare",
          order: 4,
          points: [
            {
              title: "Electronic Health Records (EHR) Integration",
              description: "Seamless, secure systems that support clinical workflows, patient management, and compliance",
              order: 1
            },
            {
              title: "Telemedicine Platforms",
              description: "Scalable virtual care solutions for remote consultations, follow-ups, and mental health services",
              order: 2
            },
            {
              title: "Medical Education Technology",
              description: "Interactive learning platforms and simulation tools for medical training",
              order: 3
            },
            {
              title: "Healthcare Analytics & Reporting",
              description: "Data-driven insights for public health monitoring, policy planning, and institutional improvement",
              order: 4
            },
            {
              title: "IT Infrastructure & Support Services",
              description: "Robust network solutions, cloud services, and 24/7 technical support for uninterrupted healthcare delivery and education",
              order: 5
            }
          ]
        }
      ];
      
      // Add each service to Firestore
      for (let i = 0; i < servicesData.length; i++) {
        await setDoc(doc(db, 'services', `service-${i + 1}`), servicesData[i]);
      }
    }
  } catch (error) {
    console.error("Error initializing database:", error);
  }
};