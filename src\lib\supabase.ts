import { createClient } from '@supabase/supabase-js'

// Supabase configuration - use environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://vyxzwyuwolasytrocumo.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ5eHp3eXV3b2xhc3l0cm9jdW1vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDAzNTcsImV4cCI6MjA2NjMxNjM1N30.uH4IrkwmemxGV7onZVfmWNeyQcfwaoYWIOIOXK1MBpM'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database helper functions
export const saveContact = async (contactData: any) => {
  try {
    const { data, error } = await supabase
      .from('contacts')
      .insert([{
        ...contactData,
        created_at: new Date().toISOString(),
      }])
      .select()

    if (error) {
      console.error("Error saving contact:", error);
      return { success: false, error };
    }

    console.log("Contact saved successfully:", data);
    return { success: true, data };
  } catch (error) {
    console.error("Error saving contact:", error);
    return { success: false, error };
  }
};

export const getProjects = async () => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching projects:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching projects:", error);
    return [];
  }
};

export const getTeamMembers = async () => {
  try {
    const { data, error } = await supabase
      .from('team')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching team members:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching team members:", error);
    return [];
  }
};

export const getBlogPosts = async () => {
  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .select(`
        id,
        title,
        content,
        excerpt,
        author,
        image_url,
        tags,
        published,
        featured,
        slug,
        meta_title,
        meta_description,
        created_at,
        updated_at,
        published_at
      `)
      .eq('published', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching blog posts:", error);
      return [];
    }

    console.log("Blog posts fetched from Supabase:", data?.length || 0);
    console.log("Sample blog post data:", data?.[0]); // Log first post to see structure
    return data || [];
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    return [];
  }
};

export const getAllBlogPosts = async () => {
  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching all blog posts:", error);
      return [];
    }

    console.log("All blog posts fetched from Supabase:", data?.length || 0);
    return data || [];
  } catch (error) {
    console.error("Error fetching all blog posts:", error);
    return [];
  }
};

export const getHomepageContent = async () => {
  try {
    const { data, error } = await supabase
      .from('homepage_content')
      .select('*')
      .limit(1)
      .single();

    if (error) {
      console.error("Error fetching homepage content:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching homepage content:", error);
    return null;
  }
};

export const getBlogPageContent = async () => {
  try {
    // Get ALL blog page content to see what's available
    const { data, error } = await supabase
      .from('blog_page_content')
      .select('*');

    if (error) {
      console.error("Error fetching blog page content:", error);
      return null;
    }

    console.log("All blog page content fetched:", data);
    
    // Return the first record or null if none found
    return data && data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error("Error fetching blog page content:", error);
    return null;
  }
};

export const getServices = async () => {
  try {
    const { data, error } = await supabase
      .from('services')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching services:", error);
      return [];
    }

    console.log("Raw services data from Supabase:", data);
    console.log("First service sample:", data?.[0]);
    
    return data || [];
  } catch (error) {
    console.error("Error fetching services:", error);
    return [];
  }
};

export const updateHomepageContent = async (contentData: any) => {
  try {
    const { data, error } = await supabase
      .from('homepage_content')
      .upsert([{
        id: 1, // Assuming single homepage content record
        ...contentData,
        updated_at: new Date().toISOString(),
      }])
      .select();

    if (error) {
      console.error("Error updating homepage content:", error);
      return { success: false, error };
    }

    console.log("Homepage content updated successfully:", data);
    return { success: true, data };
  } catch (error) {
    console.error("Error updating homepage content:", error);
    return { success: false, error };
  }
};

export const saveBlogPost = async (postData: any) => {
  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .insert([{
        ...postData,
        created_at: new Date().toISOString(),
      }])
      .select()

    if (error) {
      console.error("Error saving blog post:", error);
      return { success: false, error };
    }

    console.log("Blog post saved successfully:", data);
    return { success: true, data };
  } catch (error) {
    console.error("Error saving blog post:", error);
    return { success: false, error };
  }
};

export const saveBlogPageContent = async (contentData: any) => {
  try {
    const { data, error } = await supabase
      .from('blog_page_content')
      .upsert([{
        section_name: 'blog_main',
        active: true,
        ...contentData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()

    if (error) {
      console.error("Error saving blog page content:", error);
      return { success: false, error };
    }

    console.log("Blog page content saved successfully:", data);
    return { success: true, data };
  } catch (error) {
    console.error("Error saving blog page content:", error);
    return { success: false, error };
  }
};

// Initialize database with sample data if needed
export const initializeDatabaseIfEmpty = async () => {
  try {
    // Check if projects exist
    const projects = await getProjects();
    if (projects.length === 0) {
      // Sample projects data
      const projectsData = [
        {
          title: "E-Commerce Platform",
          description: "A full-featured e-commerce platform with payment integration and inventory management.",
          image_url: "https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "web",
          technologies: ["React", "Node.js", "MongoDB", "Stripe"],
          live_url: "https://example.com/ecommerce",
          github_url: "https://github.com/example/ecommerce"
        },
        {
          title: "Financial Dashboard",
          description: "Interactive financial dashboard with real-time data visualization and analytics.",
          image_url: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "web",
          technologies: ["Vue.js", "D3.js", "Firebase", "TailwindCSS"],
          live_url: "https://example.com/dashboard",
          github_url: "https://github.com/example/dashboard"
        },
        {
          title: "Healthcare Mobile App",
          description: "Patient management mobile application for healthcare providers with telehealth features.",
          image_url: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          category: "mobile",
          technologies: ["React Native", "Firebase", "Twilio"],
          live_url: "https://example.com/healthcare-app",
          github_url: "https://github.com/example/healthcare-app"
        },
        // Add more sample projects as needed
      ];
      
      // Insert sample projects
      const { error: projectsError } = await supabase
        .from('projects')
        .insert(projectsData);
      
      if (projectsError) {
        console.error("Error inserting sample projects:", projectsError);
      }
    }
    
    // Check if team members exist
    const teamMembers = await getTeamMembers();
    if (teamMembers.length === 0) {
      // Sample team data
      const teamData = [
        {
          name: "Mohammed Abdun Noor",
          role: "Managing Member",
          bio: "Mr. Noor is a dynamic leader with multi-disciplinary expertise and a proven track record across diverse industries.",
          image_url: "/team/mohammed.jpg",
          email: "<EMAIL>",
          linkedin: "https://www.linkedin.com/in/mohammed-abdun-noor-1b130b37/"
        },
        {
          name: "Dr. Santanu Das",
          role: "Technology and Business Advisor",
          bio: "Dr. Santanu Das is a seasoned technology executive and entrepreneur with a distinguished career spanning over four decades.",
          image_url: "/team/santanu.jpg",
          email: "<EMAIL>",
          linkedin: "https://www.linkedin.com/in/dr-santanu-das-3917201b/"
        }
      ];
      
      // Insert sample team members
      const { error: teamError } = await supabase
        .from('team')
        .insert(teamData);
      
      if (teamError) {
        console.error("Error inserting sample team members:", teamError);
      }
    }

    console.log("Database initialization complete");
  } catch (error) {
    console.error("Error initializing database:", error);
  }
};

// Export the client for direct use if needed
export default supabase; 