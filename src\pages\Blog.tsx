import { useState, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { Calendar, User, Search, Tag, ChevronDown, ExternalLink } from "lucide-react";
import { useRoute, Link, useLocation } from "wouter";
import { useBlogContent } from "@/hooks/use-firestore-data";

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  author: string;
  date: string;
  imageUrl?: string;
  tags: string[];
  published?: boolean;
  featured?: boolean;
  slug?: string;
  meta_title?: string;
  meta_description?: string;
  created_at?: string;
  updated_at?: string;
  published_at?: string;
}

// No static blog posts - all data comes from Supabase

export default function Blog() {
  const { data: blogContent, loading: blogContentLoading, error: blogContentError } = useBlogContent();

  // Debug logging
  console.log("Blog component - blogContent loading:", blogContentLoading);
  console.log("Blog component - blogContent:", blogContent);
  console.log("Blog component - blogContent error:", blogContentError);

  // State for interactive elements
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [activeCard, setActiveCard] = useState<string | null>(null);
  
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [allTags, setAllTags] = useState<string[]>([]);
  const [, params] = useRoute("/blog/:id");
  const [currentPost, setCurrentPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pageContent, setPageContent] = useState<any>(null);

  useEffect(() => {
    window.scrollTo(0, 0);
    
    // Handle mouse movement for interactive effects
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth - 0.5,
        y: e.clientY / window.innerHeight - 0.5
      });
    };
    
    window.addEventListener("mousemove", handleMouseMove);
    
    // Async function to load blog content and posts
    const loadBlogContent = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const { getBlogPosts, getBlogPageContent } = await import("@/lib/firebase");
        
        // Fetch blog page content
        console.log("Fetching blog page content from Firestore...");
        const pageContentData = await getBlogPageContent();
        console.log("Page content data received:", pageContentData);
        setPageContent(pageContentData);
        
        // Set dynamic document title
        document.title = `${pageContentData?.page_title || pageContentData?.title || "Blog"} | ${pageContentData?.company_name || "Next Generation Innovation"}`;
        
        console.log("Fetching blog posts from Firestore...");
        const supabasePosts = await getBlogPosts();
        
        console.log("Firestore posts received:", supabasePosts);
        
        let finalPosts: BlogPost[] = [];
        
        if (supabasePosts && supabasePosts.length > 0) {
          console.log("Processing Firestore posts...");
          // Firestore data is already in the correct format
          finalPosts = supabasePosts;
          console.log("Final posts after processing:", finalPosts);
        } else {
          console.log("No blog posts found in Firestore database");
          finalPosts = [];
        }
        
        setBlogPosts(finalPosts);
        setFilteredPosts(finalPosts);
        
        // Extract all unique tags
        const tags = new Set<string>();
        finalPosts.forEach((post: BlogPost) => {
          if (Array.isArray(post.tags)) {
            post.tags.forEach((tag: string) => tags.add(tag));
          }
        });
        setAllTags(Array.from(tags));
        
        console.log("Blog posts loaded successfully:", finalPosts.length, "posts");
        
      } catch (error) {
        console.error("Error fetching blog posts from Firestore:", error);
        setError(pageContent?.error_message || "Failed to load blog posts. Please try again later.");
        // Set empty array on error
        setBlogPosts([]);
        setFilteredPosts([]);
        setAllTags([]);
        console.log("Failed to load blog posts, showing empty state");
      } finally {
        setLoading(false);
      }
    };
    
    loadBlogContent();
    
    // Set up auto-refresh every 30 seconds to check for new content
    const refreshInterval = setInterval(loadBlogContent, 30000);
    
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      clearInterval(refreshInterval);
    };
  }, []);

  // Parallax scrolling effect
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 1000], [0, 200]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0.5]);

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px -10px rgba(0, 206, 209, 0.2)",
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  // Scroll to next section function
  const scrollToNextSection = () => {
    const heroSection = document.querySelector('section');
    if (heroSection) {
      const heroHeight = heroSection.offsetHeight;
      window.scrollTo({
        top: heroHeight - 50,
        behavior: 'smooth'
      });
    }
  };

  // Handle search and filtering
  useEffect(() => {
    let result = blogPosts;
    
    // Apply search term filter
    if (searchTerm) {
      result = result.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply tag filter
    if (selectedTag) {
      result = result.filter(post => post.tags.includes(selectedTag));
    }
    
    setFilteredPosts(result);
  }, [searchTerm, selectedTag, blogPosts]);



  // Handle single post view
  useEffect(() => {
    if (params && params.id) {
      const post = blogPosts.find(p => p.id === params.id);
      if (post) {
        setCurrentPost(post);
        // Set document title using meta_title if available, otherwise use title
        document.title = `${post.meta_title || post.title} | ${pageContent?.company_name || "Next Generation Innovation"}`;
        
        // Set meta description if available
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription && (post.meta_description || post.excerpt)) {
          metaDescription.setAttribute('content', post.meta_description || post.excerpt || '');
        }
      }
    } else {
      setCurrentPost(null);
      document.title = `${pageContent?.page_title || "Blog"} | ${pageContent?.company_name || "Next Generation Innovation"}`;
      
      // Reset meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', pageContent?.meta_description || 'Read the latest insights, industry updates, and thought leadership articles from Next Generation Innovation.');
      }
    }
  }, [params, blogPosts]);

  // If viewing a single post
  if (currentPost) {
    return (
      <main className="bg-background text-foreground min-h-screen overflow-hidden">
        {/* Hero Section with enhanced parallax effect */}
        <section className="relative min-h-[90vh] flex items-center justify-center overflow-hidden pt-16 sm:pt-20">
          {/* Enhanced background elements with parallax */}
          <div className="absolute inset-0 bg-gradient-to-b from-background/90 to-background/95"></div>
          <motion.div 
            className="absolute inset-0 opacity-20"
            style={{ y: y1, opacity }}
          >
            <motion.div 
              className="absolute top-1/3 left-1/3 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 rounded-full bg-cyan-500 blur-3xl"
              animate={{
                x: mousePosition.x * -30,
                y: mousePosition.y * -30,
              }}
              transition={{ type: "spring", damping: 25, stiffness: 50 }}
            />
            <motion.div 
              className="absolute bottom-1/3 right-1/3 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 rounded-full bg-blue-700 blur-3xl"
              animate={{
                x: mousePosition.x * 30,
                y: mousePosition.y * 30,
              }}
              transition={{ type: "spring", damping: 25, stiffness: 50 }}
            />
          </motion.div>
          
          {/* Enhanced floating particles with more variety - reduced for mobile */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(10)].map((_, i) => (
              <motion.div
                key={i}
                className={`absolute rounded-full ${
                  i % 3 === 0 ? 'bg-cyan-500' : 
                  i % 3 === 1 ? 'bg-blue-600' : 'bg-purple-500'
                } opacity-30`}
                style={{
                  width: `${Math.random() * 6 + 2}px`,
                  height: `${Math.random() * 6 + 2}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -15, 0],
                  x: [0, Math.random() * 10 - 5, 0],
                  opacity: [0.2, 0.5, 0.2],
                  scale: [1, Math.random() * 0.5 + 1, 1],
                }}
                transition={{
                  duration: Math.random() * 5 + 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>
          
          <div className="container relative mx-auto px-4 sm:px-6 z-10">
            <motion.div 
              className="flex flex-col items-center text-center"
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
            >
              <motion.h1 
                className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6"
                variants={fadeIn}
              >
                <motion.span 
                  className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,0.8)]"
                  animate={{
                    backgroundPosition: ['0% center', '100% center', '0% center'],
                  }}
                  transition={{
                    duration: 10,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  style={{
                    backgroundSize: '200% auto',
                  }}
                >
                  {currentPost.title}
                </motion.span>
              </motion.h1>
              
              <motion.div 
                className="w-24 sm:w-32 h-1 sm:h-1.5 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mb-6 sm:mb-8"
                variants={fadeIn}
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: "6rem", opacity: 1, transition: { delay: 0.3, duration: 0.8 } }}
              />
              
              {/* Featured Badge */}
              {currentPost.featured && (
                <motion.div 
                  className="mb-4"
                  variants={fadeIn}
                >
                  <span className="px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full font-medium text-sm">
                    {pageContent?.featured_article_text || "Featured Article"}
                  </span>
                </motion.div>
              )}


              
              <motion.div 
                className="flex items-center justify-center space-x-6 text-foreground/80 mb-6"
                variants={fadeIn}
              >
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-cyan-500" />
                  <div className="flex flex-col text-sm">
                    <span>{pageContent?.published_label || "Published"}</span>
                    <span className="font-medium">
                      {new Date(currentPost.published_at || currentPost.created_at || currentPost.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                </div>
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-2 text-cyan-500" />
                  <div className="flex flex-col text-sm">
                    <span>{pageContent?.author_label || "Author"}</span>
                    <span className="font-medium">{currentPost.author}</span>
                  </div>
                </div>
                {currentPost.updated_at && currentPost.updated_at !== currentPost.created_at && (
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-orange-500" />
                    <div className="flex flex-col text-sm">
                      <span>{pageContent?.updated_label || "Updated"}</span>
                      <span className="font-medium">
                        {new Date(currentPost.updated_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                  </div>
                )}
              </motion.div>
              
              <motion.div 
                className="flex flex-wrap justify-center gap-2 mb-6"
                variants={fadeIn}
              >
                {currentPost.tags.map((tag, index) => (
                  <motion.span 
                    key={index} 
                    className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(0, 206, 209, 0.2)" }}
                  >
                    {tag}
                  </motion.span>
                ))}
              </motion.div>
            </motion.div>
          </div>
        </section>

        <section className="py-8">
          <div className="container mx-auto px-4 sm:px-6">
            
            <motion.div 
              className="prose prose-lg dark:prose-invert max-w-3xl mx-auto bg-card backdrop-blur-md border border-border rounded-xl p-6 sm:p-8 shadow-xl shadow-cyan-500/5 relative overflow-hidden"
              variants={cardVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Animated background gradient */}
              <motion.div 
                className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-700/5 opacity-0"
                animate={{ 
                  opacity: 0.5,
                  backgroundPosition: ['0% center', '100% center'],
                }}
                transition={{ 
                  opacity: { duration: 0.3 },
                  backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" } 
                }}
                style={{ backgroundSize: '200% auto' }}
              />
              
              <div className="relative z-10">
                {currentPost.content ? (
                  currentPost.content.split('\n').map((paragraph, index) => (
                    <motion.p 
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.1 * index }}
                      className="text-foreground/80"
                    >
                      {paragraph}
                    </motion.p>
                  ))
                ) : (
                  <motion.div 
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4 }}
                    className="text-foreground/80"
                  >
                    <p className="mb-4">
                      This article covers important topics related to {currentPost.tags.join(', ')} and provides valuable insights for professionals in the field.
                    </p>
                    <p className="mb-4">
                      Our team at {currentPost.author} has compiled comprehensive research and analysis to bring you the most up-to-date information.
                    </p>
                    <p>
                      Stay tuned for more detailed content and follow our blog for the latest updates in {currentPost.tags[0]?.toLowerCase() || 'innovation'}.
                    </p>
                  </motion.div>
                )}
              </div>
            </motion.div>
            
            <motion.div 
              className="mt-12 max-w-3xl mx-auto text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <motion.a 
                href="/blog" 
                className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-700 text-white rounded-full shadow-lg shadow-cyan-500/30 font-medium text-base sm:text-lg relative overflow-hidden group inline-flex items-center gap-2"
                whileHover={{ scale: 1.05, boxShadow: "0 10px 25px -5px rgba(0, 206, 209, 0.5)" }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  <span>{pageContent?.back_to_blog_text || "Back to Blog"}</span>
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              </motion.a>
            </motion.div>
          </div>
        </section>
      </main>
    );
  }

  // Blog listing page
  return (
    <main className="bg-background text-foreground min-h-screen overflow-hidden">
      {/* Hero Section with enhanced parallax effect */}
      <section className="relative min-h-[90vh] flex items-center justify-center overflow-hidden pt-16 sm:pt-20">
        {/* Enhanced background elements with parallax */}
        <div className="absolute inset-0 bg-gradient-to-b from-background/90 to-background/95"></div>
        <motion.div 
          className="absolute inset-0 opacity-20"
          style={{ y: y1, opacity }}
        >
          <motion.div 
            className="absolute top-1/3 left-1/3 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 rounded-full bg-cyan-500 blur-3xl"
            animate={{
              x: mousePosition.x * -30,
              y: mousePosition.y * -30,
            }}
            transition={{ type: "spring", damping: 25, stiffness: 50 }}
          />
          <motion.div 
            className="absolute bottom-1/3 right-1/3 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 rounded-full bg-blue-700 blur-3xl"
            animate={{
              x: mousePosition.x * 30,
              y: mousePosition.y * 30,
            }}
            transition={{ type: "spring", damping: 25, stiffness: 50 }}
          />
        </motion.div>
        
        {/* Enhanced floating particles with more variety - reduced for mobile */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className={`absolute rounded-full ${
                i % 3 === 0 ? 'bg-cyan-500' : 
                i % 3 === 1 ? 'bg-blue-600' : 'bg-purple-500'
              } opacity-30`}
              style={{
                width: `${Math.random() * 6 + 2}px`,
                height: `${Math.random() * 6 + 2}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -15, 0],
                x: [0, Math.random() * 10 - 5, 0],
                opacity: [0.2, 0.5, 0.2],
                scale: [1, Math.random() * 0.5 + 1, 1],
              }}
              transition={{
                duration: Math.random() * 5 + 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
        
        <div className="container relative mx-auto px-4 sm:px-6 z-10">
          <motion.div 
            className="flex flex-col items-center text-center"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >

            
            <motion.h1 
              className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-bold mb-4 sm:mb-6"
              variants={fadeIn}
            >
              <motion.span 
                className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,0.8)]"
                animate={{
                  backgroundPosition: ['0% center', '100% center', '0% center'],
                }}
                transition={{
                  duration: 10,
                  repeat: Infinity,
                  ease: "linear"
                }}
                style={{
                  backgroundSize: '200% auto',
                }}
              >
                {blogContentLoading ? "Our Blog" : (blogContent?.heroTitle || pageContent?.hero_title || pageContent?.title || pageContent?.page_title || "Our Blog")}
              </motion.span>
            </motion.h1>
            
            <motion.div 
              className="w-24 sm:w-32 h-1 sm:h-1.5 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mb-6 sm:mb-8"
              variants={fadeIn}
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: "6rem", opacity: 1, transition: { delay: 0.3, duration: 0.8 } }}
            />
            
            <motion.p
              className="text-base sm:text-lg md:text-xl lg:text-2xl text-center text-foreground max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-3xl mx-auto mb-8 sm:mb-12"
              variants={fadeIn}
            >
              {blogContentLoading ? "Insights, news, and updates from our team" : (blogContent?.heroSubtitle || pageContent?.hero_subtitle || pageContent?.subtitle || pageContent?.description || "Insights, news, and updates from our team")}
            </motion.p>
            
            <motion.div
              className="mt-4 sm:mt-8"
              variants={fadeIn}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <button 
                onClick={scrollToNextSection}
                className="flex flex-col items-center text-muted-foreground hover:text-cyan-400 transition-colors duration-300"
              >
                <span className="mb-2">{pageContent?.hero_explore_text || pageContent?.explore_text || pageContent?.cta_text || "Explore Articles"}</span>
                <motion.div
                  animate={{ y: [0, 8, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  <ChevronDown size={20} className="sm:w-6 sm:h-6" />
                </motion.div>
              </button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <section className="py-12 sm:py-16 md:py-24">
        <div className="container mx-auto px-4 sm:px-6">
          {/* Search, Filter, and Refresh */}
          <motion.div 
            className="mb-8 flex flex-col md:flex-row gap-4 bg-card backdrop-blur-md border border-border rounded-xl p-6 shadow-xl shadow-cyan-500/5 relative overflow-hidden"
            variants={cardVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >

            {/* Animated background gradient */}
            <motion.div 
              className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-700/5 opacity-0"
              animate={{ 
                opacity: 0.5,
                backgroundPosition: ['0% center', '100% center'],
              }}
              transition={{ 
                opacity: { duration: 0.3 },
                backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" } 
              }}
              style={{ backgroundSize: '200% auto' }}
            />
            
            <div className="relative z-10 flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-cyan-500" />
                </div>
                <input
                  type="text"
                  placeholder={pageContent?.search_placeholder || pageContent?.search_text || "Search blog posts..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full p-3 rounded-md border border-border bg-background/50 focus:border-cyan-500 focus:ring-cyan-500"
                />
              </div>
            </div>
            
            <div className="relative z-10 flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Tag className="h-5 w-5 text-cyan-500" />
                </div>
                <select
                  value={selectedTag || ""}
                  onChange={(e) => setSelectedTag(e.target.value || null)}
                  className="pl-10 w-full p-3 rounded-md border border-border bg-background/50 focus:border-cyan-500 focus:ring-cyan-500 appearance-none"
                >
                  <option value="">{pageContent?.all_tags_text || pageContent?.tags_text || "All Tags"}</option>
                  {allTags.map((tag) => (
                    <option key={tag} value={tag}>{tag}</option>
                  ))}
                </select>
              </div>
            </div>
          </motion.div>
          
          {/* Blog Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              // Loading state
              [...Array(6)].map((_, index) => (
                <motion.div
                  key={index}
                  className="bg-card backdrop-blur-md border border-border rounded-xl overflow-hidden shadow-xl shadow-cyan-500/5 relative"
                  variants={cardVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                >
                  <div className="h-48 bg-gradient-to-r from-cyan-500/10 to-blue-700/10 animate-pulse" />
                  <div className="p-6">
                    <div className="flex gap-2 mb-3">
                      <div className="h-5 w-16 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded animate-pulse" />
                      <div className="h-5 w-20 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded animate-pulse" />
                    </div>
                    <div className="h-6 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded mb-2 animate-pulse" />
                    <div className="h-4 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded mb-1 animate-pulse" />
                    <div className="h-4 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded mb-4 animate-pulse" />
                    <div className="flex justify-between">
                      <div className="h-4 w-24 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded animate-pulse" />
                      <div className="h-4 w-32 bg-gradient-to-r from-cyan-500/20 to-blue-700/20 rounded animate-pulse" />
                    </div>
                  </div>
                </motion.div>
              ))
            ) : error ? (
              // Error state
              <motion.div 
                className="col-span-full text-center py-12 bg-card backdrop-blur-md border border-border rounded-xl p-6 shadow-xl shadow-cyan-500/5 relative overflow-hidden"
                variants={cardVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
              >
                <motion.div 
                  className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-orange-700/5 opacity-0"
                  animate={{ 
                    opacity: 0.5,
                    backgroundPosition: ['0% center', '100% center'],
                  }}
                  transition={{ 
                    opacity: { duration: 0.3 },
                    backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" } 
                  }}
                  style={{ backgroundSize: '200% auto' }}
                />
                
                <div className="relative z-10">
                  <p className="text-xl text-red-600 dark:text-red-400 mb-4">{error}</p>
                  <motion.button
                    onClick={() => window.location.reload()}
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-orange-600 text-white rounded-full shadow-lg shadow-red-500/30 font-medium text-base sm:text-lg relative overflow-hidden group inline-flex items-center gap-2"
                    whileHover={{ scale: 1.05, boxShadow: "0 10px 25px -5px rgba(239, 68, 68, 0.5)" }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="relative z-10">{pageContent?.retry_button_text || "Retry"}</span>
                    <span className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  </motion.button>
                </div>
              </motion.div>
            ) : filteredPosts.length > 0 ? (
              filteredPosts.map((post) => (
                <motion.a 
                  key={post.id} 
                  href={`/blog/${post.id}`}
                  className="bg-card backdrop-blur-md border border-border rounded-xl overflow-hidden shadow-xl shadow-cyan-500/5 relative"
                  variants={cardVariants}
                  initial="hidden"
                  whileInView="visible"
                  whileHover="hover"
                  viewport={{ once: true, margin: "-100px" }}
                  onHoverStart={() => setActiveCard(post.id)}
                  onHoverEnd={() => setActiveCard(null)}
                  onClick={() => window.location.href = `/blog/${post.id}`}
                >
                  {/* Animated background gradient */}
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-700/5 opacity-0"
                    animate={{ 
                      opacity: activeCard === post.id ? 0.5 : 0,
                      backgroundPosition: ['0% center', '100% center'],
                    }}
                    transition={{ 
                      opacity: { duration: 0.3 },
                      backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" } 
                    }}
                    style={{ backgroundSize: '200% auto' }}
                  />
                  

                  
                  <div className="p-6 relative z-10">
                    <div className="flex flex-wrap gap-2 mb-3">
                      {post.featured && (
                        <motion.span 
                          className="px-2 py-0.5 text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-md font-medium"
                          whileHover={{ scale: 1.05 }}
                        >
                          {pageContent?.featured_badge_text || "Featured"}
                        </motion.span>
                      )}
                      {post.tags.slice(0, post.featured ? 2 : 3).map((tag, index) => (
                        <motion.span 
                          key={index} 
                          className="px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-md"
                          whileHover={{ scale: 1.05, backgroundColor: "rgba(0, 206, 209, 0.2)" }}
                        >
                          {tag}
                        </motion.span>
                      ))}
                      {post.tags.length > (post.featured ? 2 : 3) && (
                        <span className="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md">
                          +{post.tags.length - (post.featured ? 2 : 3)}
                        </span>
                      )}
                    </div>
                    
                    <h3 className="text-xl font-bold mb-2 line-clamp-2">{post.title}</h3>
                    
                    <p className="text-foreground/80 mb-4 line-clamp-3">
                      {post.excerpt || post.content || `Explore insights about ${post.tags.slice(0, 2).join(' and ')} in this comprehensive article by ${post.author}.`}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-foreground/70">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1 text-cyan-500" />
                        {new Date(post.published_at || post.created_at || post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                      <div className="flex items-center">
                        <User className="w-4 h-4 mr-1 text-cyan-500" />
                        {post.author}
                      </div>
                    </div>
                  </div>
                </motion.a>
              ))
            ) : (
              // Empty state when no posts found
              <motion.div 
                className="col-span-full text-center py-12 bg-card backdrop-blur-md border border-border rounded-xl p-6 shadow-xl shadow-cyan-500/5 relative overflow-hidden"
                variants={cardVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
              >
                {/* Animated background gradient */}
                <motion.div 
                  className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-700/5 opacity-0"
                  animate={{ 
                    opacity: 0.5,
                    backgroundPosition: ['0% center', '100% center'],
                  }}
                  transition={{ 
                    opacity: { duration: 0.3 },
                    backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" } 
                  }}
                  style={{ backgroundSize: '200% auto' }}
                />
                
                <div className="relative z-10">
                  <p className="text-xl text-foreground/80 mb-4">
                    {blogPosts.length === 0 ? (pageContent?.no_posts_message || "No blog posts available yet.") : (pageContent?.no_filtered_posts_message || "No blog posts found matching your criteria.")}
                  </p>
                  {blogPosts.length > 0 && (
                    <motion.button
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedTag(null);
                      }}
                      className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-700 text-white rounded-full shadow-lg shadow-cyan-500/30 font-medium text-base sm:text-lg relative overflow-hidden group inline-flex items-center gap-2"
                      whileHover={{ scale: 1.05, boxShadow: "0 10px 25px -5px rgba(0, 206, 209, 0.5)" }}
                      whileTap={{ scale: 0.98 }}
                    >
                                              <span className="relative z-10">{pageContent?.clear_filters_text || "Clear Filters"}</span>
                      <span className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    </motion.button>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </section>
    </main>
  );
}