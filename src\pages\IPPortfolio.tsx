import React, { useState, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { FileText, Award, Lightbulb, BookOpen, Calendar, User, ExternalLink, ChevronDown } from "lucide-react";
import { getIPPortfolioContent, getPatents, getPapers } from "../lib/firebase";

// Define types for our data
interface Patent {
  id?: string;
  title: string;
  patentNumber: string;
  year: string;
  grantedDate: string;
  description: string;
  category: string;
  link?: string;
  status?: string;
  createdAt?: any;
  updatedAt?: any;
}

interface Publication {
  id?: string;
  title: string;
  conference: string;
  year: string;
  authors: string[];
  abstract: string;
  pages: string;
  doi: string;
  link?: string;
  status?: string;
  createdAt?: any;
  updatedAt?: any;
}

export default function IPPortfolio() {
  // State for interactive elements
  const [activeSection, setActiveSection] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [ipContent, setIpContent] = useState<any>(null);
  const [dynamicPatents, setDynamicPatents] = useState<Patent[]>([]);
  const [dynamicPapers, setDynamicPapers] = useState<Publication[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    document.title = "Intelectual Property | Next Generation Innovation";
    window.scrollTo(0, 0);
    
    // Handle mouse movement for interactive effects
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth - 0.5,
        y: e.clientY / window.innerHeight - 0.5
      });
    };
    
    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  // Fetch IP portfolio content from Firestore
  useEffect(() => {
    const fetchAllContent = async () => {
      try {
        setLoading(true);
        
        // Fetch all content in parallel
        const [content, patentsData, papersData] = await Promise.all([
          getIPPortfolioContent(),
          getPatents(),
          getPapers()
        ]);
        
        setIpContent(content);
        setDynamicPatents(patentsData);
        setDynamicPapers(papersData);
      } catch (error) {
        console.error("Error fetching IP portfolio content:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAllContent();
  }, []);

  // Parallax scrolling effect
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 1000], [0, 200]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0.5]);

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px -10px rgba(0, 206, 209, 0.2)",
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  // Scroll to next section function
  const scrollToNextSection = () => {
    const heroSection = document.querySelector('section');
    if (heroSection) {
      const heroHeight = heroSection.offsetHeight;
      window.scrollTo({
        top: heroHeight - 50,
        behavior: 'smooth'
      });
    }
  };

  // Patents data
  const patents: Patent[] = [
    {
      id: "1",
      title: "Cost optimization of wireless-enabled metering infrastructures",
      patentNumber: "US 8619657",
      year: "2013",
      grantedDate: "12/31/2013",
      description: "Granted 12/31/2013. This patent focuses on optimizing costs in wireless-enabled metering infrastructures for IoT applications.",
      category: "Internet of Things (IoT)",
      link: "https://patents.google.com/patent/US8619657B2/en?oq=8619657+"
    },
    {
      id: "2",
      title: "Methods systems, and devices for robustness improvement in a mobile ad hoc network using reputation-based routing",
      patentNumber: "US 8774192",
      year: "2014",
      grantedDate: "7/8/2014",
      description: "Granted 7/8/2014. This patent introduces methods and systems for improving robustness in mobile ad hoc networks through reputation-based routing.",
      category: "Networking",
      link: "https://patents.google.com/patent/US8774192B2/en?oq=8774192+"
    },
    {
      id: "3",
      title: "Systems, devices, and methods of managing power consumption in wireless sensor networks",
      patentNumber: "US 8532008",
      year: "2013",
      grantedDate: "9/10/2013",
      description: "Granted 9/10/2013. This patent presents innovative approaches to managing power consumption in wireless sensor networks, extending operational life and efficiency.",
      category: "Internet of Things (IoT)",
      link: "https://patents.google.com/patent/US8532008B2/en?oq=8532008+"
    },
    {
      id: "4",
      title: "Cost optimization of wireless-enabled metering infrastructures",
      patentNumber: "US 9161246",
      year: "2015",
      grantedDate: "10/13/2015",
      description: "Granted 10/13/2015. This patent builds upon previous work to further optimize costs in wireless-enabled metering infrastructures for IoT applications.",
      category: "Internet of Things (IoT)",
      link: "https://patents.google.com/patent/US9161246B2/en?oq=9161246+"
    },
    {
      id: "5",
      title: "Methods systems, and devices for robustness improvement in a mobile ad hoc network using reputation-based routing",
      patentNumber: "US 9226182",
      year: "2015",
      grantedDate: "12/29/2015",
      description: "Granted 12/29/2015. This patent extends previous work on improving robustness in mobile ad hoc networks through reputation-based routing mechanisms.",
      category: "Networking",
      link: "https://patents.google.com/patent/US9226182B2/en?oq=9226182+"
    }
  ];

  // Publications data
  const publications: Publication[] = [
    {
      id: "1",
      title: "Fault-Tolerant IoT System Using Software-Based \"Digital Twin\"",
      conference: "WF-IoT 2024",
      year: "2024",
      authors: ["Baranwal", "Verma", "Das", "Hassan"],
      abstract: "A comprehensive analysis of fault-tolerant IoT systems using digital twin technology.",
      pages: "828-833",
      doi: "",
      link: "https://dblp.org/db/conf/wf-iot/wf-iot2024.html#BaranwalVDH24",
      status: "published"
    }
  ];

  // Show loading state while fetching content
  if (loading) {
    return (
      <main className="bg-background text-foreground min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto mb-4"></div>
          <p className="text-foreground/70">Loading IP Portfolio content...</p>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-background text-foreground min-h-screen overflow-hidden">
      {/* Hero Section with enhanced parallax effect */}
      <section className="relative min-h-[90vh] flex items-center justify-center overflow-hidden pt-16 sm:pt-20">
        {/* Enhanced background elements with parallax */}
        <div className="absolute inset-0 bg-gradient-to-b from-background/90 to-background/95"></div>
        <motion.div 
          className="absolute inset-0 opacity-20"
          style={{ y: y1, opacity }}
        >
          <motion.div 
            className="absolute top-1/3 left-1/3 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 rounded-full bg-cyan-500 blur-3xl"
            animate={{
              x: mousePosition.x * -30,
              y: mousePosition.y * -30,
            }}
            transition={{ type: "spring", damping: 25, stiffness: 50 }}
          />
          <motion.div 
            className="absolute bottom-1/3 right-1/3 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 rounded-full bg-blue-700 blur-3xl"
            animate={{
              x: mousePosition.x * 30,
              y: mousePosition.y * 30,
            }}
            transition={{ type: "spring", damping: 25, stiffness: 50 }}
          />
        </motion.div>
        
        {/* Enhanced floating particles with more variety - reduced for mobile */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className={`absolute rounded-full ${
                i % 3 === 0 ? 'bg-cyan-500' : 
                i % 3 === 1 ? 'bg-blue-600' : 'bg-purple-500'
              } opacity-30`}
              style={{
                width: `${Math.random() * 6 + 2}px`,
                height: `${Math.random() * 6 + 2}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -15, 0],
                x: [0, Math.random() * 10 - 5, 0],
                opacity: [0.2, 0.5, 0.2],
                scale: [1, Math.random() * 0.5 + 1, 1],
              }}
              transition={{
                duration: Math.random() * 5 + 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
        
        <div className="container relative mx-auto px-4 sm:px-6 z-10">
          <motion.div 
            className="flex flex-col items-center text-center"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.h1 
              className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-bold mb-4 sm:mb-6"
              variants={fadeIn}
            >
              <motion.span 
                className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,0.8)]"
                animate={{
                  backgroundPosition: ['0% center', '100% center', '0% center'],
                }}
                transition={{
                  duration: 10,
                  repeat: Infinity,
                  ease: "linear"
                }}
                style={{
                  backgroundSize: '200% auto',
                }}
              >
                {ipContent?.heroTitle || "Our Intellectual Property"}
              </motion.span>
            </motion.h1>
            
            <motion.div 
              className="w-24 sm:w-32 h-1 sm:h-1.5 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mb-6 sm:mb-8"
              variants={fadeIn}
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: "6rem", opacity: 1, transition: { delay: 0.3, duration: 0.8 } }}
            />
            
            <motion.p
              className="text-base sm:text-lg md:text-xl lg:text-2xl text-center text-foreground max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-3xl mx-auto mb-8 sm:mb-12"
              variants={fadeIn}
            >
              {ipContent?.heroDescription || "The company principals are well-known researchers and innovators with numerous patents in various IT domains including Internet of Things (IoT), Cybersecurity, Blockchain, and AI applications"}
            </motion.p>
            
            <motion.div
              className="mt-4 sm:mt-8"
              variants={fadeIn}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <button 
                onClick={scrollToNextSection}
                className="flex flex-col items-center text-muted-foreground hover:text-cyan-400 transition-colors duration-300"
              >
                <span className="mb-2">Explore Our Patents</span>
                <motion.div
                  animate={{ y: [0, 8, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  <ChevronDown size={20} className="sm:w-6 sm:h-6" />
                </motion.div>
              </button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 relative z-10 py-12 sm:py-16 md:py-24">
        {/* Patents section with enhanced visuals and interactivity */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-cyan-500 to-blue-700 flex items-center justify-center mb-6 shadow-lg shadow-cyan-500/20 mx-auto">
            <Award className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,0.8)]">
              {ipContent?.patentsSectionTitle || "U.S. Patents"}
            </span>
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mb-4 mx-auto"></div>
          <p className="text-foreground/80 max-w-2xl mx-auto">
            {ipContent?.patentsIntroText || "Our principals have been granted a large number of patents in different areas of the IT space, including Internet of Things (IoT), Cybersecurity, Blockchain, and Applications of AI in Natural Language Processing."}
          </p>
        </motion.div>
        
        {/* Patents cards with enhanced visuals and interactivity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {(dynamicPatents.length > 0 ? dynamicPatents : patents).map((patent, index) => (
            <motion.div
              key={patent.id}
              className="bg-card backdrop-blur-md border border-border rounded-xl p-6 sm:p-8 shadow-xl shadow-cyan-500/5 relative overflow-hidden"
              variants={cardVariants}
              initial="hidden"
              whileInView="visible"
              whileHover="hover"
              viewport={{ once: true }}
              onHoverStart={() => setActiveSection(index)}
              onHoverEnd={() => setActiveSection(null)}
            >
              {/* Animated background gradient */}
              <motion.div 
                className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-700/5 opacity-0"
                animate={{ 
                  opacity: activeSection === index ? 0.5 : 0,
                  backgroundPosition: ['0% center', '100% center'],
                }}
                transition={{ 
                  opacity: { duration: 0.3 },
                  backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" } 
                }}
                style={{ backgroundSize: '200% auto' }}
              />

              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-cyan-500/20 to-blue-700/20 flex items-center justify-center mr-3">
                    <Lightbulb className="w-5 h-5 text-cyan-500" />
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-xs font-medium px-2 py-1 rounded-full bg-cyan-500/10 text-cyan-500 dark:text-cyan-400">
                      {patent.category}
                    </span>
                    {patent.status && (
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        patent.status === 'active' 
                          ? 'bg-green-500/10 text-green-500 dark:text-green-400' 
                          : 'bg-yellow-500/10 text-yellow-500 dark:text-yellow-400'
                      }`}>
                        {patent.status}
                      </span>
                    )}
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-foreground mb-2">{patent.title}</h3>
                <p className="text-foreground/80 mb-4">{patent.description}</p>
                
                <div className="border-t border-border pt-4 mt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center text-sm text-foreground/70">
                      <FileText className="w-4 h-4 mr-2 text-cyan-500" />
                      {patent.patentNumber}
                    </div>
                    <div className="flex items-center text-sm text-foreground/70">
                      <Calendar className="w-4 h-4 mr-2 text-cyan-500" />
                      {patent.grantedDate}
                    </div>
                    {patent.link && (
                      <div className="flex items-center text-sm">
                        <a 
                          href={patent.link} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="flex items-center text-cyan-500 hover:text-cyan-600 transition-colors px-2 py-1"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          View Patent
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Papers section with enhanced visuals */}
        <motion.div
          className="bg-card backdrop-blur-md border border-border rounded-xl p-6 sm:p-8 shadow-xl shadow-cyan-500/5 relative overflow-hidden mt-16"
          variants={cardVariants}
          initial="hidden"
          whileInView="visible"
          whileHover="hover"
          viewport={{ once: true }}
        >
          <div className="flex flex-col items-center text-center relative z-10">
            <motion.div 
              className="w-16 h-16 rounded-full bg-gradient-to-br from-cyan-500 to-blue-700 flex items-center justify-center shadow-lg shadow-cyan-500/20 mb-6"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 260, damping: 20 }}
            >
              <BookOpen className="w-8 h-8 text-white" />
            </motion.div>
            
            <motion.h3 
              className="text-2xl font-bold mb-4"
              variants={fadeIn}
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,0.8)]">
                {ipContent?.papersSectionTitle || "Papers"}
              </span>
            </motion.h3>
            
            <motion.div 
              className="w-16 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mb-4"
              variants={fadeIn}
            />
            
            <motion.p
              className="text-foreground/80 max-w-2xl mx-auto mb-6"
              variants={fadeIn}
            >
              {ipContent?.papersIntroText || "The principals have 30 papers published in peer-reviewed journals and in conference proceedings. Few examples are as follows:"}
            </motion.p>
            
            {/* Papers list with enhanced styling */}
            <div className="space-y-4 w-full max-w-3xl mx-auto">
              {(dynamicPapers.length > 0 ? dynamicPapers : publications).map((paper, index) => (
                <motion.div 
                  key={paper.id || index}
                  className="w-full bg-background/50 dark:bg-gray-800/30 backdrop-blur-md border border-border rounded-xl p-6 hover:shadow-lg hover:shadow-cyan-500/10 transition-all duration-300"
                  whileHover={{ y: -5 }}
                  variants={fadeIn}
                >
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:w-1/4">
                      <div className="flex items-center mb-4">
                        <BookOpen className="w-5 h-5 text-cyan-500 mr-2" />
                        <span className="text-foreground font-medium">{paper.conference}</span>
                      </div>
                      <div className="flex items-center mb-4">
                        <Calendar className="w-5 h-5 text-cyan-500 mr-2" />
                        <span className="text-foreground/70">{paper.year}</span>
                      </div>
                      <div className="flex items-center mb-4">
                        <span className="text-foreground/70">Pages: {paper.pages}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-foreground/70">{paper.authors.join(', ')}</span>
                      </div>
                      {paper.status && (
                        <div className="flex items-center mt-2">
                          <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                            paper.status === 'published' 
                              ? 'bg-green-500/10 text-green-500 dark:text-green-400' 
                              : 'bg-yellow-500/10 text-yellow-500 dark:text-yellow-400'
                          }`}>
                            {paper.status}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <div className="md:w-3/4">
                      <h3 className="text-xl font-bold text-foreground mb-3">{paper.title}</h3>
                      {paper.abstract && (
                        <p className="text-foreground/70 mb-4">{paper.abstract}</p>
                      )}
                      {paper.link && (
                        <div className="mt-4">
                          <a 
                            href={paper.link} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="flex items-center text-cyan-500 hover:text-cyan-600 transition-colors"
                          >
                            <ExternalLink className="w-4 h-4 mr-2" />
                            View Publication
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
        
        {/* Enhanced Call to action */}
        <motion.div
          className="text-center mt-8 sm:mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.7 }}
          viewport={{ once: true }}
        >
          <a href="/contact">
            <motion.button
              className="px-6 sm:px-8 md:px-10 py-3 sm:py-4 bg-gradient-to-r from-cyan-500 to-blue-700 text-white rounded-full shadow-lg shadow-cyan-500/30 font-medium text-base sm:text-lg relative overflow-hidden group w-full sm:w-auto"
              whileHover={{ scale: 1.05, boxShadow: "0 10px 25px -5px rgba(0, 206, 209, 0.5)" }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                <span>Contact Us</span>
                <motion.span
                  animate={{ x: [0, 5, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  <ExternalLink size={16} className="sm:w-[18px] sm:h-[18px]" />
                </motion.span>
              </span>
              <span className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            </motion.button>
          </a>
        </motion.div>
      </div>
    </main>
  );
}